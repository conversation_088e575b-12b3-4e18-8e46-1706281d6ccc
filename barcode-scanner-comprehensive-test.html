<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Comprehensive Barcode Scanner Test - iCalDZ</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .test-header {
            background: linear-gradient(135deg, #16a085, #2c3e50);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .test-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .test-header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .test-content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #16a085;
        }

        .test-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .test-section h3 {
            color: #34495e;
            margin: 15px 0 10px 0;
            font-size: 1.2rem;
        }

        .test-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            font-family: 'Courier New', monospace;
        }

        .test-input:focus {
            border-color: #16a085;
            box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.1);
            outline: none;
        }

        .test-button {
            background: linear-gradient(135deg, #16a085, #2c3e50);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(22, 160, 133, 0.3);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .status-panel {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .test-result {
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            font-weight: bold;
        }

        .test-result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .test-result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .test-result.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .test-result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .test-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: 2px solid #e0e0e0;
        }

        .test-card.active {
            border-color: #16a085;
            box-shadow: 0 5px 15px rgba(22, 160, 133, 0.2);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #16a085, #2c3e50);
            transition: width 0.3s ease;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #16a085;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.success { color: #2ecc71; }
        .log-entry.error { color: #e74c3c; }
        .log-entry.warning { color: #f39c12; }
        .log-entry.info { color: #3498db; }

        @media (max-width: 768px) {
            .test-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .test-content {
                padding: 20px;
            }
            
            .test-header {
                padding: 20px;
            }
            
            .test-header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 Comprehensive Barcode Scanner Test</h1>
            <p>Advanced testing system for long-term barcode scanner stability</p>
        </div>
        
        <div class="test-content">
            <!-- System Status Panel -->
            <div class="test-section">
                <h2>📊 System Status & Health Monitoring</h2>
                <div id="systemStatus" class="status-panel">
                    Initializing comprehensive test system...
                </div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="totalTests">0</div>
                        <div class="stat-label">Total Tests</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="successfulTests">0</div>
                        <div class="stat-label">Successful</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="failedTests">0</div>
                        <div class="stat-label">Failed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="uptime">0s</div>
                        <div class="stat-label">Uptime</div>
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="healthProgress" style="width: 100%"></div>
                </div>
            </div>

            <!-- Test Controls -->
            <div class="test-section">
                <h2>🎮 Test Controls</h2>
                <button class="test-button" onclick="startComprehensiveTest()">🚀 Start Comprehensive Test</button>
                <button class="test-button" onclick="startLongTermTest()">⏰ Start Long-term Test (24h)</button>
                <button class="test-button" onclick="startStressTest()">💪 Start Stress Test</button>
                <button class="test-button warning" onclick="resetTestSystem()">🔄 Reset Test System</button>
                <button class="test-button danger" onclick="stopAllTests()">🛑 Stop All Tests</button>
            </div>

            <!-- Barcode Scanner Test Fields -->
            <div class="test-section">
                <h2>📷 Barcode Scanner Test Fields</h2>
                <div class="test-grid">
                    <div class="test-card" id="dashboardCard">
                        <h3>🏠 Dashboard Scanner</h3>
                        <input type="text" class="test-input barcode-input" placeholder="Dashboard barcode scanner - امسح الباركود" 
                               id="dashboard-scanner" onkeydown="logScannerEvent(event, 'Dashboard')" />
                        <div class="test-result info" id="dashboardResult">Ready for testing...</div>
                    </div>
                    
                    <div class="test-card" id="salesCard">
                        <h3>🛒 Sales Scanner</h3>
                        <input type="text" class="test-input barcode-input" placeholder="Sales barcode scanner - مسح منتج للفاتورة" 
                               class="sales-scanner" onkeydown="logScannerEvent(event, 'Sales')" />
                        <div class="test-result info" id="salesResult">Ready for testing...</div>
                    </div>
                    
                    <div class="test-card" id="editCard">
                        <h3>✏️ Edit Scanner</h3>
                        <input type="text" class="test-input barcode-input" placeholder="Edit scanner - مسح للتعديل" 
                               class="edit-scanner" onkeydown="logScannerEvent(event, 'Edit')" />
                        <div class="test-result info" id="editResult">Ready for testing...</div>
                    </div>
                    
                    <div class="test-card" id="productCard">
                        <h3>📦 Product Scanner</h3>
                        <input type="text" class="test-input barcode-input" placeholder="Product barcode - باركود المنتج" 
                               id="product-barcode" onkeydown="logScannerEvent(event, 'Product')" />
                        <div class="test-result info" id="productResult">Ready for testing...</div>
                    </div>
                </div>
            </div>

            <!-- Test Log -->
            <div class="test-section">
                <h2>📋 Comprehensive Test Log</h2>
                <div id="testLog" class="log-container">
                    <div class="log-entry info">[INFO] Comprehensive test system initialized</div>
                    <div class="log-entry info">[INFO] Ready to begin testing...</div>
                </div>
                <button class="test-button" onclick="clearTestLog()">🗑️ Clear Log</button>
                <button class="test-button" onclick="exportTestResults()">📄 Export Results</button>
            </div>
        </div>
    </div>

    <script src="barcode-scanner-comprehensive-test.js"></script>
</body>
</html>
