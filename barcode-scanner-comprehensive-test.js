/**
 * 🔧 Comprehensive Barcode Scanner Test System
 * Advanced testing framework for long-term barcode scanner stability
 * 
 * Features:
 * - Long-term stability testing (24+ hours)
 * - Memory leak detection
 * - Event handler conflict testing
 * - Focus management validation
 * - Cross-modal contamination testing
 * - Performance monitoring
 */

class ComprehensiveBarcodeTest {
    constructor() {
        this.startTime = Date.now();
        this.testStats = {
            totalTests: 0,
            successfulTests: 0,
            failedTests: 0,
            memoryLeaks: 0,
            focusFailures: 0,
            eventConflicts: 0
        };
        
        this.isRunning = false;
        this.testIntervals = [];
        this.testTimeouts = [];
        this.memoryBaseline = null;
        
        // Test configurations
        this.testConfigs = {
            comprehensive: {
                duration: 30 * 60 * 1000, // 30 minutes
                interval: 5000, // 5 seconds
                tests: ['focus', 'input', 'memory', 'events', 'isolation']
            },
            longTerm: {
                duration: 24 * 60 * 60 * 1000, // 24 hours
                interval: 60000, // 1 minute
                tests: ['focus', 'input', 'memory', 'events', 'isolation', 'stress']
            },
            stress: {
                duration: 10 * 60 * 1000, // 10 minutes
                interval: 100, // 100ms
                tests: ['rapid-input', 'focus-switching', 'memory-stress']
            }
        };
        
        this.init();
    }
    
    init() {
        this.log('🚀 Comprehensive Barcode Scanner Test System Initialized', 'info');
        this.updateSystemStatus();
        this.setupMemoryBaseline();
        this.startUptimeCounter();
        
        // Simulate KeyboardShortcuts system for testing
        this.setupMockKeyboardShortcuts();
    }
    
    setupMockKeyboardShortcuts() {
        window.KeyboardShortcuts = {
            isEnabled: true,
            barcodeProtectionEnabled: true,
            eventCount: 0,
            barcodeProtectionCount: 0,
            
            isBarcodeInputActive: (event) => {
                const target = event.target;
                if (!target || target.tagName !== 'INPUT') return false;
                
                const barcodeIndicators = [
                    'barcode-input', 'scanner-input', 'dashboard-scanner',
                    'sales-scanner', 'edit-scanner', 'product-barcode'
                ];
                
                return barcodeIndicators.some(indicator => 
                    target.className.includes(indicator) || 
                    target.id.includes(indicator)
                );
            },
            
            resetBarcodeSystem: () => {
                this.log('🔄 KeyboardShortcuts: Barcode system reset', 'info');
                return true;
            },
            
            getStatus: () => ({
                isEnabled: this.isEnabled,
                barcodeProtectionEnabled: this.barcodeProtectionEnabled,
                eventCount: this.eventCount,
                barcodeProtectionCount: this.barcodeProtectionCount
            })
        };
    }
    
    setupMemoryBaseline() {
        if (performance.memory) {
            this.memoryBaseline = {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                timestamp: Date.now()
            };
            this.log(`📊 Memory baseline: ${Math.round(this.memoryBaseline.used / 1024 / 1024)}MB`, 'info');
        }
    }
    
    startUptimeCounter() {
        setInterval(() => {
            const uptime = Math.floor((Date.now() - this.startTime) / 1000);
            document.getElementById('uptime').textContent = this.formatUptime(uptime);
        }, 1000);
    }
    
    formatUptime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }
    
    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${type}`;
        logEntry.textContent = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
        
        const logContainer = document.getElementById('testLog');
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
        
        console.log(`[BarcodeTest] ${message}`);
    }
    
    updateStats() {
        document.getElementById('totalTests').textContent = this.testStats.totalTests;
        document.getElementById('successfulTests').textContent = this.testStats.successfulTests;
        document.getElementById('failedTests').textContent = this.testStats.failedTests;
        
        // Update health progress
        const successRate = this.testStats.totalTests > 0 ? 
            (this.testStats.successfulTests / this.testStats.totalTests) * 100 : 100;
        document.getElementById('healthProgress').style.width = `${successRate}%`;
    }
    
    updateSystemStatus() {
        const status = document.getElementById('systemStatus');
        const memoryInfo = performance.memory ? {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
        } : null;
        
        status.innerHTML = `
🔧 System Status: ${this.isRunning ? 'RUNNING' : 'IDLE'}
📊 Tests: ${this.testStats.totalTests} total, ${this.testStats.successfulTests} success, ${this.testStats.failedTests} failed
🧠 Memory: ${memoryInfo ? `${memoryInfo.used}MB / ${memoryInfo.total}MB` : 'N/A'}
⌨️ KeyboardShortcuts: ${window.KeyboardShortcuts ? 'Active' : 'Inactive'}
🛡️ Barcode Protection: ${window.KeyboardShortcuts?.barcodeProtectionEnabled ? 'Enabled' : 'Disabled'}
🔄 Event Count: ${window.KeyboardShortcuts?.eventCount || 0}
📷 Scanner Events: ${window.KeyboardShortcuts?.barcodeProtectionCount || 0}
        `;
    }
    
    // Test Methods
    async testFocusManagement() {
        this.log('🎯 Testing focus management...', 'info');
        
        const scanners = [
            { id: 'dashboard-scanner', name: 'Dashboard' },
            { element: document.querySelector('.sales-scanner'), name: 'Sales' },
            { element: document.querySelector('.edit-scanner'), name: 'Edit' },
            { id: 'product-barcode', name: 'Product' }
        ];
        
        let focusTests = 0;
        let focusSuccess = 0;
        
        for (const scanner of scanners) {
            const element = scanner.id ? document.getElementById(scanner.id) : scanner.element;
            if (element) {
                focusTests++;
                try {
                    element.focus();
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                    if (document.activeElement === element) {
                        focusSuccess++;
                        this.log(`✅ ${scanner.name} scanner focus: SUCCESS`, 'success');
                    } else {
                        this.log(`❌ ${scanner.name} scanner focus: FAILED`, 'error');
                        this.testStats.focusFailures++;
                    }
                } catch (e) {
                    this.log(`❌ ${scanner.name} scanner focus: ERROR - ${e.message}`, 'error');
                    this.testStats.focusFailures++;
                }
            }
        }
        
        return focusSuccess === focusTests;
    }
    
    async testInputHandling() {
        this.log('⌨️ Testing input handling...', 'info');
        
        const testCodes = ['000000001', '123456789', 'ABC123', 'TEST001'];
        const scanners = document.querySelectorAll('.barcode-input');
        
        let inputTests = 0;
        let inputSuccess = 0;
        
        for (const scanner of scanners) {
            for (const code of testCodes) {
                inputTests++;
                try {
                    // Simulate barcode input
                    scanner.value = code;
                    scanner.dispatchEvent(new Event('input', { bubbles: true }));
                    
                    // Test keyboard event
                    const keyEvent = new KeyboardEvent('keydown', {
                        key: 'Enter',
                        bubbles: true
                    });
                    scanner.dispatchEvent(keyEvent);
                    
                    await new Promise(resolve => setTimeout(resolve, 50));
                    
                    inputSuccess++;
                    this.log(`✅ Input test: ${code} in ${scanner.className}`, 'success');
                } catch (e) {
                    this.log(`❌ Input test failed: ${code} - ${e.message}`, 'error');
                }
            }
        }
        
        return inputSuccess === inputTests;
    }
    
    async testMemoryUsage() {
        this.log('🧠 Testing memory usage...', 'info');
        
        if (!performance.memory) {
            this.log('⚠️ Memory API not available', 'warning');
            return true;
        }
        
        const currentMemory = performance.memory.usedJSHeapSize;
        const memoryIncrease = currentMemory - this.memoryBaseline.used;
        const memoryIncreaseMB = Math.round(memoryIncrease / 1024 / 1024);
        
        this.log(`📊 Memory usage: ${Math.round(currentMemory / 1024 / 1024)}MB (+${memoryIncreaseMB}MB)`, 'info');
        
        // Consider it a memory leak if increase is more than 50MB
        if (memoryIncreaseMB > 50) {
            this.log(`⚠️ Potential memory leak detected: +${memoryIncreaseMB}MB`, 'warning');
            this.testStats.memoryLeaks++;
            return false;
        }
        
        return true;
    }
    
    async testEventConflicts() {
        this.log('⚡ Testing event conflicts...', 'info');
        
        const scanner = document.getElementById('dashboard-scanner');
        if (!scanner) return false;
        
        let conflictTests = 0;
        let conflictSuccess = 0;
        
        // Test function keys while scanner is focused
        const functionKeys = ['F1', 'F2', 'F3', 'F4', 'F5'];
        
        scanner.focus();
        
        for (const key of functionKeys) {
            conflictTests++;
            try {
                const event = new KeyboardEvent('keydown', {
                    key: key,
                    bubbles: true
                });
                
                scanner.dispatchEvent(event);
                await new Promise(resolve => setTimeout(resolve, 50));
                
                // Check if KeyboardShortcuts detected barcode input
                if (window.KeyboardShortcuts.isBarcodeInputActive({ target: scanner })) {
                    conflictSuccess++;
                    this.log(`✅ Event conflict test: ${key} properly handled`, 'success');
                } else {
                    this.log(`❌ Event conflict test: ${key} not detected as barcode input`, 'error');
                    this.testStats.eventConflicts++;
                }
            } catch (e) {
                this.log(`❌ Event conflict test failed: ${key} - ${e.message}`, 'error');
                this.testStats.eventConflicts++;
            }
        }
        
        return conflictSuccess === conflictTests;
    }
    
    async runTestSuite(config) {
        this.log(`🚀 Starting ${config.duration / 60000} minute test suite...`, 'info');
        
        const testInterval = setInterval(async () => {
            this.testStats.totalTests++;
            
            let allTestsPassed = true;
            
            if (config.tests.includes('focus')) {
                allTestsPassed &= await this.testFocusManagement();
            }
            
            if (config.tests.includes('input')) {
                allTestsPassed &= await this.testInputHandling();
            }
            
            if (config.tests.includes('memory')) {
                allTestsPassed &= await this.testMemoryUsage();
            }
            
            if (config.tests.includes('events')) {
                allTestsPassed &= await this.testEventConflicts();
            }
            
            if (allTestsPassed) {
                this.testStats.successfulTests++;
            } else {
                this.testStats.failedTests++;
            }
            
            this.updateStats();
            this.updateSystemStatus();
            
        }, config.interval);
        
        this.testIntervals.push(testInterval);
        
        // Stop test after duration
        const stopTimeout = setTimeout(() => {
            clearInterval(testInterval);
            this.log(`✅ Test suite completed after ${config.duration / 60000} minutes`, 'success');
            
            if (this.testIntervals.length === 1) {
                this.isRunning = false;
                this.updateSystemStatus();
            }
        }, config.duration);
        
        this.testTimeouts.push(stopTimeout);
    }
}

// Global test instance
let comprehensiveTest;

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    comprehensiveTest = new ComprehensiveBarcodeTest();
});

// Test control functions
function startComprehensiveTest() {
    if (comprehensiveTest.isRunning) {
        comprehensiveTest.log('⚠️ Test already running', 'warning');
        return;
    }
    
    comprehensiveTest.isRunning = true;
    comprehensiveTest.runTestSuite(comprehensiveTest.testConfigs.comprehensive);
}

function startLongTermTest() {
    if (comprehensiveTest.isRunning) {
        comprehensiveTest.log('⚠️ Test already running', 'warning');
        return;
    }
    
    comprehensiveTest.isRunning = true;
    comprehensiveTest.runTestSuite(comprehensiveTest.testConfigs.longTerm);
}

function startStressTest() {
    if (comprehensiveTest.isRunning) {
        comprehensiveTest.log('⚠️ Test already running', 'warning');
        return;
    }
    
    comprehensiveTest.isRunning = true;
    comprehensiveTest.runTestSuite(comprehensiveTest.testConfigs.stress);
}

function resetTestSystem() {
    stopAllTests();
    
    // Reset stats
    comprehensiveTest.testStats = {
        totalTests: 0,
        successfulTests: 0,
        failedTests: 0,
        memoryLeaks: 0,
        focusFailures: 0,
        eventConflicts: 0
    };
    
    comprehensiveTest.setupMemoryBaseline();
    comprehensiveTest.updateStats();
    comprehensiveTest.updateSystemStatus();
    comprehensiveTest.log('🔄 Test system reset', 'info');
}

function stopAllTests() {
    comprehensiveTest.testIntervals.forEach(clearInterval);
    comprehensiveTest.testTimeouts.forEach(clearTimeout);
    comprehensiveTest.testIntervals = [];
    comprehensiveTest.testTimeouts = [];
    comprehensiveTest.isRunning = false;
    comprehensiveTest.updateSystemStatus();
    comprehensiveTest.log('🛑 All tests stopped', 'warning');
}

function clearTestLog() {
    document.getElementById('testLog').innerHTML = '';
    comprehensiveTest.log('🗑️ Test log cleared', 'info');
}

function exportTestResults() {
    const results = {
        timestamp: new Date().toISOString(),
        uptime: Date.now() - comprehensiveTest.startTime,
        stats: comprehensiveTest.testStats,
        memoryBaseline: comprehensiveTest.memoryBaseline,
        currentMemory: performance.memory ? {
            used: performance.memory.usedJSHeapSize,
            total: performance.memory.totalJSHeapSize
        } : null
    };
    
    const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `barcode-test-results-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    comprehensiveTest.log('📄 Test results exported', 'success');
}

function logScannerEvent(event, scannerType) {
    comprehensiveTest.log(`📷 ${scannerType} scanner event: ${event.key}`, 'info');

    // Test barcode protection
    if (window.KeyboardShortcuts && window.KeyboardShortcuts.isBarcodeInputActive) {
        const isProtected = window.KeyboardShortcuts.isBarcodeInputActive(event);
        comprehensiveTest.log(`🛡️ Barcode protection: ${isProtected ? 'ACTIVE' : 'INACTIVE'}`,
                             isProtected ? 'success' : 'warning');
    }
}

// Additional test scenarios for edge cases
function testModalTransitions() {
    comprehensiveTest.log('🔄 Testing modal transitions...', 'info');

    // Simulate modal opening/closing scenarios
    const scenarios = [
        { name: 'Dashboard to Sales', from: 'dashboard', to: 'sales' },
        { name: 'Sales to Edit', from: 'sales', to: 'edit' },
        { name: 'Edit to Dashboard', from: 'edit', to: 'dashboard' }
    ];

    scenarios.forEach(scenario => {
        comprehensiveTest.log(`🔄 Testing transition: ${scenario.name}`, 'info');

        // Simulate state changes that would happen during modal transitions
        const fromScanner = document.querySelector(`.${scenario.from}-scanner, #${scenario.from}-scanner`);
        const toScanner = document.querySelector(`.${scenario.to}-scanner, #${scenario.to}-scanner`);

        if (fromScanner && toScanner) {
            // Test focus transfer
            fromScanner.focus();
            setTimeout(() => {
                toScanner.focus();
                if (document.activeElement === toScanner) {
                    comprehensiveTest.log(`✅ Focus transfer: ${scenario.name}`, 'success');
                } else {
                    comprehensiveTest.log(`❌ Focus transfer failed: ${scenario.name}`, 'error');
                }
            }, 100);
        }
    });
}

function testRapidInputScenario() {
    comprehensiveTest.log('⚡ Testing rapid input scenario...', 'info');

    const scanner = document.getElementById('dashboard-scanner');
    if (!scanner) return;

    scanner.focus();

    // Simulate rapid barcode scanning
    const rapidCodes = ['001', '002', '003', '004', '005'];
    let index = 0;

    const rapidInterval = setInterval(() => {
        if (index >= rapidCodes.length) {
            clearInterval(rapidInterval);
            comprehensiveTest.log('✅ Rapid input test completed', 'success');
            return;
        }

        const code = rapidCodes[index];
        scanner.value = code;
        scanner.dispatchEvent(new Event('input', { bubbles: true }));

        // Simulate Enter key
        const enterEvent = new KeyboardEvent('keydown', {
            key: 'Enter',
            bubbles: true
        });
        scanner.dispatchEvent(enterEvent);

        comprehensiveTest.log(`⚡ Rapid input: ${code}`, 'info');
        index++;
    }, 50); // Very fast input - 50ms intervals
}

function testLongRunningScenario() {
    comprehensiveTest.log('⏰ Starting long-running scenario test...', 'info');

    let iterationCount = 0;
    const maxIterations = 1000; // Test for 1000 iterations

    const longRunningInterval = setInterval(() => {
        if (iterationCount >= maxIterations) {
            clearInterval(longRunningInterval);
            comprehensiveTest.log('✅ Long-running scenario completed', 'success');
            return;
        }

        // Cycle through all scanners
        const scanners = document.querySelectorAll('.barcode-input');
        const currentScanner = scanners[iterationCount % scanners.length];

        if (currentScanner) {
            currentScanner.focus();
            currentScanner.value = `LONG${iterationCount.toString().padStart(6, '0')}`;
            currentScanner.dispatchEvent(new Event('input', { bubbles: true }));

            // Clear after a short delay
            setTimeout(() => {
                currentScanner.value = '';
            }, 100);
        }

        iterationCount++;

        if (iterationCount % 100 === 0) {
            comprehensiveTest.log(`⏰ Long-running test: ${iterationCount}/${maxIterations}`, 'info');
        }
    }, 1000); // Every second
}

// Enhanced test runner with additional scenarios
function runEnhancedTests() {
    if (comprehensiveTest.isRunning) {
        comprehensiveTest.log('⚠️ Enhanced tests already running', 'warning');
        return;
    }

    comprehensiveTest.isRunning = true;
    comprehensiveTest.log('🚀 Starting enhanced test scenarios...', 'info');

    // Run additional test scenarios
    setTimeout(() => testModalTransitions(), 1000);
    setTimeout(() => testRapidInputScenario(), 5000);
    setTimeout(() => testLongRunningScenario(), 10000);

    // Mark as not running after all tests complete
    setTimeout(() => {
        comprehensiveTest.isRunning = false;
        comprehensiveTest.log('✅ Enhanced test scenarios completed', 'success');
    }, 15000);
}
