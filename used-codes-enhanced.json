{"ICAL-2025-E738-9287-726F-1B21-6737-7F10-99A7-9D2E": {"prefix": "ICAL", "year": 2025, "clientId": "MBOBC1429958127D411FC412", "clientName": "Test User", "timestamp": 1749426441122, "type": "LIFETIME", "trialDays": null, "expiryDate": null, "securityLevel": 2, "machineId": "TEST123", "allowedIPs": [], "maxDevices": 1, "geoRestriction": null, "hardwareBinding": true, "codeVersion": "2.0", "securityHash": "abf5a89f32da2aa6", "serverValidation": true, "generationTime": 1749426441122, "validationWindow": **********, "behaviorTracking": false, "networkValidation": true, "continuousMonitoring": false, "serverToken": "52d14e1b4eb0d6f1ea807a59430e13a1cfe8c84550006ae6d642c3590557eb2f", "used": false, "generatedAt": "2025-06-08T23:47:21.126Z"}, "ICAL-2025-E8F4-C875-56BA-25DF-1808-8FDA-2E7F-4A07": {"prefix": "ICAL", "year": 2025, "clientId": "MBOBGL0HA1A07E6B6847D3A0", "clientName": "zoka", "timestamp": 1749426653537, "type": "LIFETIME", "trialDays": null, "expiryDate": null, "securityLevel": 2, "machineId": "F7C681E2C5959036", "allowedIPs": [], "maxDevices": 1, "geoRestriction": null, "hardwareBinding": true, "codeVersion": "2.0", "securityHash": "14ea1a9184400bba", "serverValidation": true, "generationTime": 1749426653537, "validationWindow": **********, "behaviorTracking": false, "networkValidation": true, "continuousMonitoring": false, "serverToken": "c1c4591d4fc350c9fffe66aa507deb9364d30f30e1a9a431160879475d4dc03c", "used": false, "generatedAt": "2025-06-08T23:50:53.543Z"}, "ICAL-2025-CCEF-A0EA-88F3-FA14-8593-4436-0B97-3716": {"prefix": "ICAL", "year": 2025, "clientId": "MBOBGUIU65A353CCAF2F6E9B", "clientName": "Test", "timestamp": 1749426665863, "type": "LIFETIME", "trialDays": null, "expiryDate": null, "securityLevel": 1, "machineId": null, "allowedIPs": [], "maxDevices": 1, "geoRestriction": null, "hardwareBinding": true, "codeVersion": "2.0", "securityHash": "14dd20f722a818ef", "serverValidation": true, "generationTime": 1749426665863, "validationWindow": **********, "behaviorTracking": false, "networkValidation": false, "continuousMonitoring": false, "serverToken": "2b8f2f94b10f31a6ac890a3a3256306936bf55cc2527a6572490d8df6d3d1f81", "used": false, "generatedAt": "2025-06-08T23:51:05.869Z"}, "ICAL-2025-AA2D-CFBF-4781-12DF-C774-F606-3652-549D": {"prefix": "ICAL", "year": 2025, "clientId": "MBOBI1WRFA61F10E00B73FDF", "clientName": "zoka", "timestamp": 1749426722091, "type": "LIFETIME", "trialDays": null, "expiryDate": null, "securityLevel": 1, "machineId": "F7C681E2C5959036", "allowedIPs": [], "maxDevices": 1, "geoRestriction": null, "hardwareBinding": false, "codeVersion": "2.0", "securityHash": "5c04cbaa27925710", "serverValidation": true, "generationTime": 1749426722091, "validationWindow": **********, "behaviorTracking": false, "networkValidation": false, "continuousMonitoring": false, "serverToken": "6efcafd67d1766489e2d9d1ac5ffc6d866e290b185106e5daf9617196a99a73b", "used": false, "generatedAt": "2025-06-08T23:52:02.097Z"}, "ICAL-2025-AFFA-75BA-FD5E-F41E-3778-2C8A-BFCF-63D4": {"prefix": "ICAL", "year": 2025, "clientId": "MBOBI21CDB252B69E2BC873B", "clientName": "zoka", "timestamp": 1749426722257, "type": "LIFETIME", "trialDays": null, "expiryDate": null, "securityLevel": 2, "machineId": "F7C681E2C5959036", "allowedIPs": [], "maxDevices": 1, "geoRestriction": null, "hardwareBinding": true, "codeVersion": "2.0", "securityHash": "3ffea2b4647b4d1c", "serverValidation": true, "generationTime": 1749426722257, "validationWindow": **********, "behaviorTracking": false, "networkValidation": true, "continuousMonitoring": false, "serverToken": "7d63d8ff39b209539f553f438777c355b2b43bb0e226bfd2051e2e33b75687a8", "used": false, "generatedAt": "2025-06-08T23:52:02.257Z"}, "ICAL-2025-AFAB-91DD-E6CB-4527-1B7B-B6C9-0352-E3E9": {"prefix": "ICAL", "year": 2025, "clientId": "MBOBI24025ED81E60E74566E", "clientName": "zoka", "timestamp": 1749426722352, "type": "LIFETIME", "trialDays": null, "expiryDate": null, "securityLevel": 3, "machineId": "F7C681E2C5959036", "allowedIPs": [], "maxDevices": 1, "geoRestriction": null, "hardwareBinding": true, "codeVersion": "2.0", "securityHash": "ccc1aa6cdbbac1e3", "serverValidation": true, "generationTime": 1749426722352, "validationWindow": **********, "behaviorTracking": true, "networkValidation": true, "continuousMonitoring": true, "serverToken": "1d3baf6e0f3d1ad4dae44a6b4d9f9306a97f2bdd4d17b94277d09fd5c4231e14", "used": false, "generatedAt": "2025-06-08T23:52:02.353Z"}, "ICAL-2025-CB64-6C2F-E1F8-A749-FAFB-C4AE-4A43-EC8D": {"prefix": "ICAL", "year": 2025, "clientId": "MBOBI25VBAB3F799738DE1D4", "clientName": "zoka-trial", "timestamp": 1749426722419, "type": "TRIAL", "trialDays": 7, "expiryDate": "2025-06-15T23:52:02.419Z", "securityLevel": 2, "machineId": null, "allowedIPs": [], "maxDevices": 1, "geoRestriction": null, "hardwareBinding": true, "codeVersion": "2.0", "securityHash": "515d2224f72792f0", "serverValidation": true, "generationTime": 1749426722419, "validationWindow": **********, "behaviorTracking": false, "networkValidation": true, "continuousMonitoring": false, "serverToken": "3f05ae9910bf4ed6a7ad648a4ebece4952e6eb95a24fd87e11b7eb4f2ccbae34", "used": false, "generatedAt": "2025-06-08T23:52:02.420Z"}, "ICAL-2025-E045-09B0-FB47-8071-9BC1-1635-B965-C41A": {"prefix": "ICAL", "year": 2025, "clientId": "MBOBLQ8U88CEE26501ABA877", "clientName": "zoka2", "timestamp": 1749426893599, "type": "LIFETIME", "trialDays": null, "expiryDate": null, "securityLevel": 1, "machineId": "F7C681E2C5959036", "allowedIPs": [], "maxDevices": 1, "geoRestriction": null, "hardwareBinding": false, "codeVersion": "2.0", "securityHash": "d916aef0a1de2669", "serverValidation": true, "generationTime": 1749426893599, "validationWindow": **********, "behaviorTracking": false, "networkValidation": false, "continuousMonitoring": false, "serverToken": "24fdf7002c4b36bbe035c9e543c29c7640d368a0b4f26489278cfed24c9ab3d8", "used": false, "generatedAt": "2025-06-08T23:54:53.605Z"}, "ICAL-2025-D5AF-9F63-C39A-7FD8-E188-7678-D907-24BE": {"prefix": "ICAL", "year": 2025, "clientId": "MBOC5JXE0FCF0BF5FDF5142F", "clientName": "zoka3", "timestamp": 1749427818533, "type": "LIFETIME", "trialDays": null, "expiryDate": null, "securityLevel": 3, "machineId": "B8D07FD6B1013D94", "allowedIPs": [], "maxDevices": 1, "geoRestriction": null, "hardwareBinding": true, "codeVersion": "2.0", "securityHash": "8670b5b0f547a7e4", "serverValidation": true, "generationTime": 1749427818533, "validationWindow": **********, "behaviorTracking": true, "networkValidation": true, "continuousMonitoring": true, "serverToken": "1835af53dc5fbf440ed73a84fa3e8edbb2318b725dee70be8c6dabf4d408f10e", "used": false, "generatedAt": "2025-06-09T00:10:18.543Z"}, "ICAL-2025-B8E8-65B1-32E2-A4D8-FB85-CBBA-E4FF-5910": {"prefix": "ICAL", "year": 2025, "clientId": "MBPRUUMK9D838C764C2DB1D2", "clientName": "zaa", "timestamp": 1749514659215, "type": "LIFETIME", "trialDays": null, "expiryDate": null, "securityLevel": 2, "machineId": "AF93138C0C8CD385", "allowedIPs": [], "maxDevices": 1, "geoRestriction": null, "hardwareBinding": true, "codeVersion": "2.0", "securityHash": "8e6c2062f6f9b289", "serverValidation": true, "generationTime": 1749514659215, "validationWindow": **********, "behaviorTracking": false, "networkValidation": true, "continuousMonitoring": false, "serverToken": "ea48c445f24f485caa8361e405b93679942bf9678307466edadb74fc92a992be", "used": false, "generatedAt": "2025-06-10T00:17:39.223Z"}, "ICAL-2025-CBF7-A060-5305-3395-831C-9619-0470-53DC": {"prefix": "ICAL", "year": 2025, "clientId": "MBPRV8GT93F3628E97DE44E1", "clientName": "zaa", "timestamp": 1749514677149, "type": "LIFETIME", "trialDays": null, "expiryDate": null, "securityLevel": 1, "machineId": "AF93138C0C8CD385", "allowedIPs": [], "maxDevices": 1, "geoRestriction": null, "hardwareBinding": false, "codeVersion": "2.0", "securityHash": "4cf4a86e454beeb9", "serverValidation": true, "generationTime": 1749514677149, "validationWindow": **********, "behaviorTracking": false, "networkValidation": false, "continuousMonitoring": false, "serverToken": "31b775390d244eafb35547890bdf9ec26d478a19a3dcb4c7dddc562ee375c84b", "used": false, "generatedAt": "2025-06-10T00:17:57.152Z"}, "ICAL-2025-E01C-6706-DE77-49DC-9F89-CC26-B6C5-4A53": {"prefix": "ICAL", "year": 2025, "clientId": "MBW6BDL1428A713043F5BF1C", "clientName": "zaa", "timestamp": 1749901741957, "type": "LIFETIME", "trialDays": null, "expiryDate": null, "securityLevel": 1, "machineId": "AAB0F71CA7BC855A", "allowedIPs": [], "maxDevices": 1, "geoRestriction": null, "hardwareBinding": false, "codeVersion": "2.0", "securityHash": "6e567dfb09344815", "serverValidation": true, "generationTime": 1749901741957, "validationWindow": **********, "behaviorTracking": false, "networkValidation": false, "continuousMonitoring": false, "serverToken": "74924aec209f231cae7782e953c17912aa13fe1d9d4c15c01cef01cac59d1d5e", "used": false, "generatedAt": "2025-06-14T11:49:01.976Z"}, "ICAL-2025-46AE-818E-D6E4-2F90-2C56-8E68-F548-248C": {"prefix": "ICAL", "year": 2025, "clientId": "MCADOSU8CDD33319E159DA78", "clientName": "Licensed User", "timestamp": 1750760652032, "type": "LIFETIME", "trialDays": null, "expiryDate": null, "securityLevel": 1, "machineId": "B94F2C6F95970538", "allowedIPs": [], "maxDevices": 1, "geoRestriction": null, "hardwareBinding": false, "codeVersion": "2.0", "securityHash": "37cc6d2af269be94", "serverValidation": true, "generationTime": 1750760652032, "validationWindow": **********, "behaviorTracking": false, "networkValidation": false, "continuousMonitoring": false, "serverToken": "e7dddf01a0a355f249d664c2363db49b23a0988d05af05eab1dd95dabdba1e61", "used": false, "generatedAt": "2025-06-24T10:24:12.045Z"}}